"use client";

import { ReactNode, useEffect, useState } from "react";
import { verifyDomain } from "@/services/authService";
import { useRouter, usePathname } from "next/navigation";
import { useDomainStore } from "@/stores/domainStore";
import { toast } from "sonner";

interface DomainProviderProps {
  children: ReactNode;
}

export const DomainProvider = ({ children }: DomainProviderProps) => {
  const [loading, setLoading] = useState(true);
  const { isDomainVerified, decodedDomainData } = useDomainStore();
  const router = useRouter();
  const pathname = usePathname();

  useEffect(() => {
    const runVerify = async () => {
      try {
        // Nếu domain đã được xác thực và có decodedDomainData, bỏ qua API
        if (isDomainVerified && decodedDomainData) {
          console.log("✅ Domain already verified, skipping verification");
          setLoading(false);
          return;
        }

        let currentDomain = window.location.hostname;

        // Sử dụng domain tạm cho localhost
        if (currentDomain === "localhost" || currentDomain === "127.0.0.1") {
          currentDomain = "agent.trinhxuanthuy.id.vn";
          console.log("🔄 Using test domain for localhost:", currentDomain);
        }

        console.log("🔍 Verifying domain:", currentDomain);
        const data = await verifyDomain({ domain: currentDomain });

        if (!data) {
          throw new Error("Invalid domain data");
        }

        console.log("✅ Domain verification successful:", {
          websiteId: data.website?.website_id,
          promptId: data.prompt?._id
        });

        // Đảm bảo domain store được cập nhật
        const { setDecodedDomainData, setIsDomainVerified } = useDomainStore.getState();
        setDecodedDomainData(data);
        setIsDomainVerified(true);

        // Chỉ redirect nếu không phải trang login hoặc welcome và chưa xác thực
        if (!isDomainVerified && pathname !== "/welcome" && pathname !== "/login") {
          router.push("/welcome");
        }
      } catch (error) {
        console.error("❌ Domain verification failed:", error);
        toast.error("Không thể xác thực domain, vui lòng thử lại.");
        // Có thể redirect đến trang lỗi hoặc login nếu cần
        // router.push("/login?error=DomainVerificationFailed");
      } finally {
        setLoading(false);
      }
    };

    runVerify();
  }, [isDomainVerified, decodedDomainData, pathname, router]);

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-gray-600">Đang tải...</div>
      </div>
    );
  }

  return <>{children}</>;
};
"use client";

import { ReactNode, useEffect, useState } from "react";
import { verifyDomain } from "@/services/authService";
import { useRouter, usePathname } from "next/navigation";
import { useDomainStore } from "@/stores/domainStore";

interface DomainProviderProps {
  children: ReactNode;
}

export const DomainProvider = ({ children }: DomainProviderProps) => {
  const [loading, setLoading] = useState(true);
  const isDomainVerified = useDomainStore((state) => state.isDomainVerified);
  const router = useRouter();
  const pathname = usePathname();

  useEffect(() => {
    const runVerify = async () => {
      try {
        if (isDomainVerified) {
          console.log("✅ Domain already verified");
          setLoading(false);
          return;
        }

        let currentDomain = window.location.hostname;

        // Nếu là localhost thì dùng domain tạm để xác thực
        if (currentDomain === "localhost" || currentDomain === "127.0.0.1") {
          currentDomain = "agent.trinhxuanthuy.id.vn";
          console.log("🔄 Using test domain for localhost:", currentDomain);
        }

        console.log("🔍 Verifying domain:", currentDomain);
        const data = await verifyDomain({ domain: currentDomain });

        if (!data) {
          throw new Error("Invalid domain data");
        }

        console.log("✅ Domain verification successful:", {
          websiteId: data.website?.website_id,
          promptId: data.prompt?._id
        });

        // Chỉ redirect nếu không phải trang login hoặc welcome
        if (pathname !== "/welcome" && pathname !== "/login") {
          router.push("/welcome");
        }

      } catch (error) {
        console.error("❌ Domain verification failed:", error);
      } finally {
        setLoading(false);
      }
    };

    runVerify();
  }, [isDomainVerified, pathname, router]);

  if (loading) return <div>Loading...</div>; // Hoặc spinner nếu cần

  return <>{children}</>;
};

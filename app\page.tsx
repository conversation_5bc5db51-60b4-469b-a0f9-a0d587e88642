"use client"

import Image from 'next/image';
import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import Header from '@/components/header/Header';

export default function Home() {
    const [sidebarOpen, setSidebarOpen] = useState(true);
    const router = useRouter();
    const [currentTopicId, setCurrentTopicId] = useState<string | null>(null);
    const [isMobile, setIsMobile] = useState(false);


    useEffect(() => {
        function handleResize() {
            setIsMobile(window.innerWidth < 1024);
            if (window.innerWidth < 1024) setSidebarOpen(false);
        }
        window.addEventListener('resize', handleResize);
        if (typeof window !== 'undefined') {
            setIsMobile(window.innerWidth < 1024);
            if (window.innerWidth < 1024) setSidebarOpen(false);
        }
        return () => window.removeEventListener('resize', handleResize);
    }, []);



    return (
        <div className="flex h-screen w-full overflow-hidden bg-white dark:bg-gray-900">
            <Header />
            {/* <UserProfileInfo /> */}
        </div>
    );
}

// Conversation interfaces
export interface ConversationItem {
    _id: string;
    name: string;
    prompt_id: string;
    creat_at: string;
    updated_at: string;
}

export interface ConversationResponse {
    error: boolean;
    status: number;
    msg: string;
    data: ConversationItem[];
}

export interface UpdateDeleteConversationResponse {
    error: boolean;
    status: number;
    msg: string;
    id: string;
}
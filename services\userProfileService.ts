import { EP } from '@/configs/constants/api';
import { getApiEndpoint } from '@/helpers/handleApiUrl';
import axiosClient from '@/lib/axios';
import { getCookie } from 'cookies-next/client';
import { useUserStore } from '@/stores/userStore';
import { UserProfileResponse } from '@/types/profile';


export const getProfile = async () => {
  const { setProfile, clearProfile } = useUserStore.getState();

  try {
    // Set loading state
    useUserStore.setState({ isLoading: true });

    // Get token from cookie
    const token = getCookie('fchatai_token') as string;

    if (!token) {
      throw new Error('No authentication token found');
    }

    const { data } = await axiosClient(process.env.NEXT_PUBLIC_NODE_API_BACKEND_URL, token)
      .get<UserProfileResponse>(getApiEndpoint([EP.API, EP.V1, EP.USER, EP.PROFILE]));

    // Save profile data to store
    setProfile(data);
    console.log("Profile Data:", data);

    return data;
  } catch (error) {
    console.error("Error fetching profile:", error);

    // Clear profile on error
    clearProfile();

    // Re-throw error for component to handle
    throw error;
  } finally {
    // Clear loading state
    useUserStore.setState({ isLoading: false });
  }
}
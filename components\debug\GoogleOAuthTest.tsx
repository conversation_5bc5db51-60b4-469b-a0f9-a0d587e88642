"use client";

import { useState } from "react";
import { signIn, getSession } from "next-auth/react";
import { useDomainStore } from "@/stores/domainStore";

export const GoogleOAuthTest = () => {
  const [testing, setTesting] = useState(false);
  const [result, setResult] = useState<any>(null);
  const { isDomainVerified, decodedDomainData } = useDomainStore();

  if (process.env.NODE_ENV !== "development") {
    return null;
  }

  const testGoogleOAuth = async () => {
    setTesting(true);
    setResult(null);

    try {
      console.log("🧪 Testing Google OAuth...");
      
      // Test 1: Check domain
      console.log("1. Domain check:", {
        isDomainVerified,
        websiteId: decodedDomainData?.website?.website_id
      });

      // Test 2: Try signIn without redirect
      console.log("2. Testing signIn...");
      const signInResult = await signIn("google", { 
        redirect: false,
        callbackUrl: "/"
      });
      
      console.log("3. SignIn result:", signInResult);
      
      // Test 3: Check session
      const session = await getSession();
      console.log("4. Session after signIn:", session);
      
      setResult({
        domain: { isDomainVerified, websiteId: decodedDomainData?.website?.website_id },
        signInResult,
        session
      });
      
    } catch (error) {
      console.error("❌ OAuth test failed:", error);
      setResult({ error: error instanceof Error ? error.message : "Unknown error" });
    } finally {
      setTesting(false);
    }
  };

  return (
    <div className="fixed top-4 right-4 bg-green-900 text-white p-4 rounded-lg text-xs max-w-sm z-50 max-h-96 overflow-y-auto">
      <h3 className="font-bold mb-2">Google OAuth Test</h3>
      <button
        onClick={testGoogleOAuth}
        disabled={testing}
        className="mb-2 px-2 py-1 bg-green-600 hover:bg-green-700 rounded text-xs disabled:opacity-50"
      >
        {testing ? "Testing..." : "Test OAuth"}
      </button>
      
      {result && (
        <div className="space-y-1 text-xs">
          <div className="font-bold text-green-300">Test Result:</div>
          <pre className="whitespace-pre-wrap break-words">
            {JSON.stringify(result, null, 2)}
          </pre>
        </div>
      )}
    </div>
  );
};

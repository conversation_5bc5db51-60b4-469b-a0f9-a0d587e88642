// src/store/domainStore.ts
import { create } from 'zustand';
import type { DecodedDomainData } from '@/types/domain'; // đường dẫn tùy bạn\
import { getCookie, setCookie } from 'cookies-next/client';

interface DomainStore {
    decodedDomainData: DecodedDomainData | null;
    isDomainVerified: boolean;
    setDecodedDomainData: (data: DecodedDomainData) => void;
    clearDecodedDomainData: () => void;
    setIsDomainVerified: (verified: boolean) => void;

}

const initializeDomainVerified = (): { isDomainVerified: boolean } => {
    const isDomainVerified = getCookie('fchatai_domain_verified') as string | undefined;
    return {
        isDomainVerified: isDomainVerified === 'true',
    };
};

export const useDomainStore = create<DomainStore>((set) => {
    const initialState = initializeDomainVerified();

    return {
        decodedDomainData: null,
        isDomainVerified: initialState.isDomainVerified,
        setDecodedDomainData: (data) =>
            set({ decodedDomainData: data, isDomainVerified: true }),
        clearDecodedDomainData: () => {
            // Xóa cookie is_domain_verified
            setCookie("is_domain_verified", "", { maxAge: -1, path: "/" });
            set({ decodedDomainData: null, isDomainVerified: false });
        },
        setIsDomainVerified: (verified) => {
            // Lưu isDomainVerified vào cookie
            setCookie("is_domain_verified", verified.toString(), {
                maxAge: 7 * 24 * 60 * 60, // 7 ngày, tương tự fchatai_token
                path: "/",
                sameSite: "lax",
            });
            set({ isDomainVerified: verified });
        },
    };
});
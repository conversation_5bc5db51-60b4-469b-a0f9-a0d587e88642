// src/store/domainStore.ts
import { create } from 'zustand';
import type { DecodedDomainData } from '@/types/domain'; // đường dẫn tùy bạn

interface DomainStore {
    decodedDomainData: DecodedDomainData | null;
    isDomainVerified: boolean;
    setDecodedDomainData: (data: DecodedDomainData) => void;
    clearDecodedDomainData: () => void;
    setIsDomainVerified: (verified: boolean) => void;
}

export const useDomainStore = create<DomainStore>((set) => ({
    decodedDomainData: null,
    isDomainVerified: false,
    setDecodedDomainData: (data) => set({ decodedDomainData: data }),
    clearDecodedDomainData: () => set({ decodedDomainData: null, isDomainVerified: false }),
    setIsDomainVerified: (verified) => set({ isDomainVerified: verified }),

}));

"use client";

import { useSession } from "next-auth/react";
import { useAuth } from "@/hooks/useAuth";

export const AuthDebug = () => {
  const { data: session, status } = useSession();
  const auth = useAuth();

  if (process.env.NODE_ENV !== "development") {
    return null;
  }

  return (
    <div className="fixed bottom-4 right-4 bg-black text-white p-4 rounded-lg text-xs max-w-sm z-50">
      <h3 className="font-bold mb-2">Auth Debug</h3>
      <div className="space-y-1">
        <div>NextAuth Status: {status}</div>
        <div>Session: {session ? "✅" : "❌"}</div>
        <div>Token: {auth.hasToken ? "✅" : "❌"}</div>
        <div>Profile: {auth.hasProfile ? "✅" : "❌"}</div>
        <div>Logged In: {auth.isLoggedIn ? "✅" : "❌"}</div>
        {session?.user && (
          <div className="mt-2">
            <div>Email: {session.user.email}</div>
            <div>Name: {session.user.name}</div>
          </div>
        )}
      </div>
    </div>
  );
};

"use client";

import { useSession } from "next-auth/react";
import { useAuth } from "@/hooks/useAuth";
import { useState, useEffect } from "react";

export const AuthDebug = () => {
  const { data: session, status } = useSession();
  const auth = useAuth();
  const [errors, setErrors] = useState<string[]>([]);

  // Listen for console errors
  useEffect(() => {
    const originalError = console.error;
    console.error = (...args) => {
      const errorMessage = args.join(" ");
      if (errorMessage.includes("next-auth") || errorMessage.includes("CLIENT_FETCH_ERROR")) {
        setErrors(prev => [...prev.slice(-2), errorMessage.substring(0, 100)]);
      }
      originalError.apply(console, args);
    };

    return () => {
      console.error = originalError;
    };
  }, []);

  if (process.env.NODE_ENV !== "development") {
    return null;
  }

  return (
    <div className="fixed bottom-4 right-4 bg-black text-white p-4 rounded-lg text-xs max-w-sm z-50 max-h-96 overflow-y-auto">
      <h3 className="font-bold mb-2">Auth Debug</h3>
      <div className="space-y-1">
        <div>NextAuth Status: {status}</div>
        <div>Session: {session ? "✅" : "❌"}</div>
        <div>Token: {auth.hasToken ? "✅" : "❌"}</div>
        <div>Profile: {auth.hasProfile ? "✅" : "❌"}</div>
        <div>Logged In: {auth.isLoggedIn ? "✅" : "❌"}</div>
        {session?.user && (
          <div className="mt-2">
            <div>Email: {session.user.email}</div>
            <div>Name: {session.user.name}</div>
          </div>
        )}
        {errors.length > 0 && (
          <div className="mt-2 border-t border-gray-600 pt-2">
            <div className="font-bold text-red-400">Recent Errors:</div>
            {errors.map((error, index) => (
              <div key={index} className="text-red-300 text-xs break-words">
                {error}
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

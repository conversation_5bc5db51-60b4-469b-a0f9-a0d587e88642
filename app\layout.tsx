import type React from "react"
import "@/app/globals.css"
import { Inter } from "next/font/google"
import { ToastContainer } from "react-toastify"
import { Metadata } from "next"
import { Providers } from "@/components/providers/providers"
import { AuthDebug } from "@/components/auth/AuthDebug"
import { DomainDebug } from "@/components/debug/DomainDebug"
import { GoogleOAuthTest } from "@/components/debug/GoogleOAuthTest"

const inter = Inter({ subsets: ["latin"] })

export const metadata: Metadata = {
  title: "Data.Fchat.ai - <PERSON>ho kiến thức AI thông minh",
  description: "<PERSON><PERSON> kiến thức AI thông minh của FChat.AI - Tr<PERSON> lý AI của người Việt, hỗ trợ đa ngôn ngữ và tích hợp công nghệ tiên tiến",
  keywords: ["AI", "chatbot", "trợ lý AI", "Việt Nam", "FChat", "kho kiến thức", "AI thông minh"],
  authors: [{ name: "FChat.AI Team" }],
  creator: "FChat.AI",
  publisher: "FChat.AI",
  formatDetection: {
    email: false,
    address: false,
    telephone: false,
  },
  metadataBase: new URL(process.env.NEXT_PUBLIC_URL || 'https://data.fchat.ai'),
  alternates: {
    canonical: '/',
  },
  openGraph: {
    title: "Data.Fchat.ai - Kho kiến thức AI thông minh",
    description: "Kho kiến thức AI thông minh của FChat.AI - Trợ lý AI của người Việt, hỗ trợ đa ngôn ngữ và tích hợp công nghệ tiên tiến",
    url: process.env.NEXT_PUBLIC_URL || 'https://data.fchat.ai',
    siteName: "Data.Fchat.ai",
    images: [
      {
        url: '/og-image.png',
        width: 1200,
        height: 630,
        alt: 'Data.Fchat.ai - Kho kiến thức AI thông minh',
      },
    ],
    locale: 'vi_VN',
    type: 'website',
  },
  twitter: {
    card: 'summary_large_image',
    title: "Data.Fchat.ai - Kho kiến thức AI thông minh",
    description: "Kho kiến thức AI thông minh của FChat.AI - Trợ lý AI của người Việt",
    images: ['/og-image.png'],
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
  verification: {
    google: process.env.GOOGLE_SITE_VERIFICATION,
  },
}
export default function RootLayout({ children }: Readonly<{ children: React.ReactNode }>) {
  return (
    <html lang="vi" suppressHydrationWarning>
      <head>
        <link rel="canonical" href={process.env.NEXT_PUBLIC_URL} />
        <meta name="robots" content="index, follow" />
        <meta name="googlebot" content="index, follow" />
        <link rel="sitemap" href="/sitemap.xml" />
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{
            __html: JSON.stringify({
              "@context": "https://schema.org",
              "@type": "WebSite",
              "name": "Data.Fchat.ai",
              "description": "Kho kiến thức AI thông minh của FChat.AI - Trợ lý AI của người Việt",
              "url": process.env.NEXT_PUBLIC_URL || 'https://data.fchat.ai',
              "potentialAction": {
                "@type": "SearchAction",
                "target": {
                  "@type": "EntryPoint",
                  "urlTemplate": `${process.env.NEXT_PUBLIC_URL || 'https://data.fchat.ai'}/search?q={search_term_string}`
                },
                "query-input": "required name=search_term_string"
              },
              "publisher": {
                "@type": "Organization",
                "name": "FChat.AI",
                "url": "https://fchat.ai"
              }
            })
          }}
        />
      </head>
      <body id="data_fchat_ai" className={inter.className} suppressHydrationWarning>
        <Providers>
          {children}
          <ToastContainer />
          <AuthDebug />
          <DomainDebug />
          <GoogleOAuthTest />
        </Providers>
      </body>
    </html>
  )
}

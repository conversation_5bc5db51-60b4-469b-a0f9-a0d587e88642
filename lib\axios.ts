import axios, { AxiosInstance, AxiosResponse, AxiosError } from "axios";
import { getCookie } from "cookies-next/client";
const axiosClient = (baseURL: string | undefined, token?: string) => {
    const axiosInstance: AxiosInstance = axios.create({
        responseType: "json",
        baseURL: baseURL,
        headers: {
            "Content-Type": "application/json",
        }
    })

    axiosInstance.interceptors.request.use(
        async (config) => {
            if (token) {
                config.headers.token = token;
            } else {
                const cookieToken = getCookie('fchatai_token') as string | undefined;
                if (cookieToken) {
                    config.headers.token = cookieToken;
                }
            }

            return config;
        },
        (error: AxiosError) => {
            return Promise.reject(error);
        }
    );

    axiosInstance.interceptors.response.use(
        (response: AxiosResponse) => {
            return response;
        },
        (error: AxiosError) => {
            // Xử lý lỗi ở đây, ví dụ như redirect đến trang login nếu token hết hạn
            if (error.response && error.response.status === 401) {

            }
            return Promise.reject(error);
        }
    );
    return axiosInstance;
}
export default axiosClient;
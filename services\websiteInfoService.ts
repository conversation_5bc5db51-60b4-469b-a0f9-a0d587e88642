import { useDomainStore } from "@/stores/domainStore";

export const useWebsiteInfo = () => {
    const data = useDomainStore((state) => state.decodedDomainData);

    const websiteInfo = data?.prompt.website;
    console.log("Website Info from Store:", websiteInfo);

    return {
        websiteInfo,
        isLoading: !data,
        isVerified: !!data,
        setDecodedDomainData: useDomainStore.getState().setDecodedDomainData,
        clearDecodedDomainData: useDomainStore.getState().clearDecodedDomainData,
    };
};
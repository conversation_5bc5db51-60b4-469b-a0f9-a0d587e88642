import { useEffect, useState } from 'react';
import { useUserStore } from '@/stores/userStore';
import { getProfile } from '@/services/userProfileService';

export const useProfile = () => {
  const { profile, isLoading } = useUserStore();
  const [error, setError] = useState<string | null>(null);

  const fetchProfile = async () => {
    try {
      setError(null);
      await getProfile();
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to fetch profile';
      setError(errorMessage);
      console.error('Profile fetch error:', err);
    }
  };

  const refetchProfile = () => {
    fetchProfile();
  };

  return {
    profile,
    isLoading,
    error,
    fetchProfile,
    refetchProfile,
    // Helper getters
    user: profile?.data?.user,
    shop: profile?.data?.shop,
    package: profile?.data?.package,
  };
};

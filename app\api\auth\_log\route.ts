import { NextRequest, NextResponse } from "next/server";

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    
    // Log the NextAuth error for debugging
    console.error("NextAuth Client Error:", {
      level: body.level,
      message: body.message,
      code: body.code,
      url: body.url,
      timestamp: new Date().toISOString()
    });
    
    return NextResponse.json({ success: true });
  } catch (error) {
    console.error("Log API error:", error);
    return NextResponse.json({ success: false }, { status: 500 });
  }
}

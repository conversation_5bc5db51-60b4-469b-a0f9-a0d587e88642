import NextAuth, { NextAuthOptions } from "next-auth";
import GoogleProvider from "next-auth/providers/google";
import { loginWithGoogle } from "@/services/authService";
import { useDomainStore } from "@/stores/domainStore";
import { PayloadLoginGoogle } from "@/services/authService";

export const authOptions: NextAuthOptions = {
  providers: [
    GoogleProvider({
      clientId: process.env.GOOGLE_CLIENT_ID || "",
      clientSecret: process.env.GOOGLE_CLIENT_SECRET || "",
      authorization: {
        params: {
          prompt: "consent",
          access_type: "offline",
          response_type: "code",
        },
      },
    }),
  ],
  callbacks: {
    async signIn({ user, account, profile }) {
      try {
        // Lấy website_id từ domainStore
        const decodedDomainData = useDomainStore.getState().decodedDomainData;

        // Chuẩn bị payload cho API backend
        const payload: PayloadLoginGoogle = {
          email: user.email || "",
          name: user.name || "",
          avatar: user.image || "",
          website_id: decodedDomainData?.website.website_id || "",
          ref: "", // Có thể lấy từ query params nếu cần
          google_id: account?.providerAccountId || "",
        };

        // Gọi API backend để lấy token
        await loginWithGoogle(payload);

        return true; // Đăng nhập thành công
      } catch (error) {
        console.error("Error during Google sign-in:", error);
        return "/login?error=GoogleSignInFailed"; // Chuyển hướng đến trang login với lỗi
      }
    },
    async jwt({ token, account }) {
      if (account) {
        console.log("🔍 OAuth account info:", {
          provider: account.provider,
          type: account.type,
          access_token: account.access_token ? "present" : "missing",
        });
        token.accessToken = account.access_token;
        token.provider = account.provider;
      }
      return token;
    },
    async session({ session, token }) {
      ;
      return session;
    },
  },
  pages: {
    signIn: "/login",
    error: "/login",
  },
  session: {
    strategy: "jwt",
  },
  secret: process.env.NEXTAUTH_SECRET,
};

export default NextAuth(authOptions);
import NextAuth, { NextAuthOptions } from "next-auth";
import GoogleProvider from "next-auth/providers/google";
import { loginWithGoogle } from "@/services/authService";
import { useDomainStore } from "@/stores/domainStore";
import { PayloadLoginGoogle } from "@/services/authService";

export const authOptions: NextAuthOptions = {
  providers: [
    GoogleProvider({
      clientId: process.env.GOOGLE_CLIENT_ID || "",
      clientSecret: process.env.GOOGLE_CLIENT_SECRET || "",
      authorization: {
        params: {
          prompt: "consent",
          access_type: "offline",
          response_type: "code",
        },
      },
    }),
  ],
  callbacks: {
    async signIn({ user, account }) {
      try {
        console.log("🔐 Starting Google sign-in process...");

        // Lấy website_id từ domainStore
        const decodedDomainData = useDomainStore.getState().decodedDomainData;

        if (!decodedDomainData?.website?.website_id) {
          console.error("❌ No website_id found in domain store");
          return "/login?error=DomainNotVerified";
        }

        // Chuẩn bị payload cho API backend
        const payload: PayloadLoginGoogle = {
          email: user.email || "",
          name: user.name || "",
          avatar: user.image || "",
          website_id: decodedDomainData.website.website_id,
          ref: process.env.NEXT_PUBLIC_REF || "", // Lấy từ env
          google_id: account?.providerAccountId || "",
        };

        console.log("📤 Sending login payload to backend:", {
          email: payload.email,
          name: payload.name,
          website_id: payload.website_id,
          google_id: payload.google_id
        });

        // Gọi API backend để lấy token
        const response = await loginWithGoogle(payload);

        if (response.error) {
          console.error("❌ Backend login failed:", response.msg);
          return "/login?error=BackendLoginFailed";
        }

        console.log("✅ Google sign-in successful");
        return true; // Đăng nhập thành công
      } catch (error) {
        console.error("❌ Error during Google sign-in:", error);
        return "/login?error=GoogleSignInFailed"; // Chuyển hướng đến trang login với lỗi
      }
    },
    async jwt({ token, account }) {
      if (account) {
        token.accessToken = account.access_token;
        token.provider = account.provider;
      }
      return token;
    },
    async session({ session, token }) {
      // Thêm id từ token vào session user
      if (session?.user && token?.sub) {
        (session.user as any).id = token.sub;
      }

      return session;
    },
    async redirect({ url, baseUrl }) {
      // Nếu URL là callback URL thì redirect về trang chủ
      if (url.startsWith(baseUrl)) {
        return url;
      }
      // Nếu URL là external thì redirect về trang chủ
      return baseUrl;
    },
  },
  pages: {
    signIn: "/login",
    error: "/login",
  },
  session: {
    strategy: "jwt",
    maxAge: 7 * 24 * 60 * 60, // 7 days
  },
  secret: process.env.NEXTAUTH_SECRET,
  debug: process.env.NODE_ENV === "development",
  logger: {
    error(code, metadata) {
      console.error("NextAuth Error:", code, metadata);
    },
    warn(code) {
      console.warn("NextAuth Warning:", code);
    },
    debug(code, metadata) {
      if (process.env.NODE_ENV === "development") {
        console.log("NextAuth Debug:", code, metadata);
      }
    },
  },
};

const handler = NextAuth(authOptions);

export { handler as GET, handler as POST };
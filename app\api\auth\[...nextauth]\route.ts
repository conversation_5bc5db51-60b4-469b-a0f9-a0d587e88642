import NextAuth, { NextAuthOptions } from "next-auth";
import GoogleProvider from "next-auth/providers/google";
import { loginWithGoogle } from "@/services/authService";
import { useDomainStore } from "@/stores/domainStore";
import { PayloadLoginGoogle } from "@/services/authService";

export const authOptions: NextAuthOptions = {
  providers: [
    GoogleProvider({
      clientId: process.env.GOOGLE_CLIENT_ID || "",
      clientSecret: process.env.GOOGLE_CLIENT_SECRET || "",
      authorization: {
        params: {
          prompt: "consent",
          access_type: "offline",
          response_type: "code",
        },
      },
    }),
  ],
  callbacks: {
    async signIn({ user, account }) {
      try {
        console.log("🔐 NextAuth signIn callback triggered", {
          provider: account?.provider,
          userEmail: user?.email,
          userName: user?.name
        });

        // Chỉ xử lý Google provider
        if (account?.provider !== "google") {
          return true;
        }

        // Lấy website_id từ domainStore hoặc fallback từ env
        const domainState = useDomainStore.getState();
        const decodedDomainData = domainState.decodedDomainData;

        // Fallback website_id từ environment variable
        const fallbackWebsiteId = process.env.NEXT_PUBLIC_WEBSITE_ID;

        console.log("🔍 Domain store state in signIn callback:", {
          isDomainVerified: domainState.isDomainVerified,
          hasDecodedData: !!decodedDomainData,
          websiteId: decodedDomainData?.website?.website_id,
          fallbackWebsiteId
        });

        const websiteId = decodedDomainData?.website?.website_id || fallbackWebsiteId;

        if (!websiteId) {
          console.error("❌ No website_id found in domain store or env during signIn");
          return "/login?error=DomainNotVerified";
        }

        // Chuẩn bị payload cho API backend
        const payload: PayloadLoginGoogle = {
          email: user.email || "",
          name: user.name || "",
          avatar: user.image || "",
          website_id: websiteId,
          ref: process.env.NEXT_PUBLIC_REF || "",
          google_id: account?.providerAccountId || "",
        };

        console.log("📤 Calling backend API with payload:", {
          email: payload.email,
          name: payload.name,
          website_id: payload.website_id,
          google_id: payload.google_id,
          ref: payload.ref
        });

        // Gọi API backend để lấy token
        const response = await loginWithGoogle(payload);

        console.log("📥 Backend response:", {
          error: response.error,
          status: response.status,
          msg: response.msg
        });

        if (response.error) {
          console.error("❌ Backend login failed:", response.msg);
          return "/login?error=BackendLoginFailed&msg=" + encodeURIComponent(response.msg || "Unknown error");
        }

        console.log("✅ Google sign-in successful, token saved to cookie");
        return true;
      } catch (error) {
        console.error("❌ Error during Google sign-in:", error);
        const errorMsg = error instanceof Error ? error.message : "Unknown error";
        return "/login?error=GoogleSignInFailed&msg=" + encodeURIComponent(errorMsg);
      }
    },
    async jwt({ token, account }) {
      if (account) {
        token.accessToken = account.access_token;
        token.provider = account.provider;
      }
      return token;
    },
    async session({ session, token }) {
      // Thêm id từ token vào session user
      if (session?.user && token?.sub) {
        (session.user as any).id = token.sub;
      }

      return session;
    },
    async redirect({ url, baseUrl }) {
      console.log("🔄 NextAuth redirect callback:", { url, baseUrl });

      // Nếu đang ở trang login và đăng nhập thành công, redirect về trang chủ
      if (url === baseUrl + "/login") {
        console.log("✅ Redirecting from login to home");
        return baseUrl + "/";
      }

      // Nếu URL bắt đầu với baseUrl thì cho phép
      if (url.startsWith(baseUrl)) {
        return url;
      }

      // Mặc định redirect về trang chủ
      console.log("🏠 Default redirect to home");
      return baseUrl + "/";
    },
  },
  pages: {
    signIn: "/login",
    error: "/login",
  },
  session: {
    strategy: "jwt",
    maxAge: 7 * 24 * 60 * 60, // 7 days
  },
  secret: process.env.NEXTAUTH_SECRET,
  debug: process.env.NODE_ENV === "development",
  logger: {
    error(code, metadata) {
      console.error("NextAuth Error:", code, metadata);
    },
    warn(code) {
      console.warn("NextAuth Warning:", code);
    },
    debug(code, metadata) {
      if (process.env.NODE_ENV === "development") {
        console.log("NextAuth Debug:", code, metadata);
      }
    },
  },
};

const handler = NextAuth(authOptions);

export { handler as GET, handler as POST };
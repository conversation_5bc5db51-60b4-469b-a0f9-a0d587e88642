"use client";

import React, { useEffect, useState } from "react";
import Image from "next/image";
import { useUserStore } from "@/stores/userStore";
import { getProfile } from "@/services/userProfileService";
import { DropdownMenu, DropdownMenuTrigger, DropdownMenuContent, DropdownMenuItem, DropdownMenuSeparator } from "@/components/ui/dropdown-menu";
import { deleteCookie } from "cookies-next/client";
import { useRouter } from 'next/navigation';

export const UserProfileInfo = () => {
    const data = useUserStore((state) => state.profile);
    const isLoading = useUserStore((state) => state.isLoading);
    const user = data?.data?.user;
    const packages = data?.data?.package;

    // Fetch profile data when component mounts
    useEffect(() => {
        const fetchUserProfile = async () => {
            if (!data) {
                try {
                    await getProfile();
                } catch (error) {
                    console.error("Failed to fetch user profile:", error);
                }
            }
        };

        fetchUserProfile();
    }, [data]);

    const name = user?.name;
    console.log("User Data:", user);
    const email = user?.email;
    const credit = packages?.credit || 0;
    const creditUse = packages?.credit_use || 0;
    const avatar = user?._id ? `https://fchat.ai/avatar/${user._id}.png` : '/favicon.ico';
    const percent = credit > 0 ? Math.round((creditUse / credit) * 100) : 0;
    const initial = name?.[0]?.toUpperCase() || email?.[0]?.toUpperCase() || 'U';

    const router = useRouter();
    const { clearProfile } = useUserStore.getState();
    const [isLoggingOut, setIsLoggingOut] = useState(false);
    const handleLogout = async () => {
        setIsLoggingOut(true);
        try {
            deleteCookie('fchatai_token');
            deleteCookie('fchatai_user_id');
            deleteCookie('fchatai_shop_id');
            clearProfile();
            router.push('/login');
        } catch (error) {
            console.error("Failed to logout:", error);
        } finally {
            setIsLoggingOut(false);
        }
    };
    // Show loading state
    if (isLoading) {
        return (
            <div className="ml-2 w-12 h-12 rounded-full bg-gray-200 animate-pulse"></div>
        );
    }

    // Show default avatar if no user data
    if (!user) {
        return (
            <button type="button" className="ml-2 flex items-center justify-center w-12 h-12 rounded-full text-xl font-semibold text-white focus:outline-none bg-gray-500">
                U
            </button>
        );
    }

    return (
        <DropdownMenu>
            <DropdownMenuTrigger asChild>
                {user._id ? (
                    <Image
                        src={avatar}
                        alt={name || 'User Avatar'}
                        width={48}
                        height={48}
                        className="ml-2 w-12 h-12 rounded-full object-cover cursor-pointer"
                        onError={(e) => {
                            // Fallback to initial avatar if image fails to load
                            e.currentTarget.style.display = 'none';
                        }}
                    />
                ) : (
                    <button type="button" className="ml-2 flex items-center justify-center w-12 h-12 rounded-full text-xl font-semibold text-white focus:outline-none bg-green-500 cursor-pointer">
                        {initial}
                    </button>
                )}
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-auto min-w-80 p-0">
                <div className="p-4 flex flex-col ">
                    <div className='flex flex-row gap-2 align-middle'>
                        {user?._id ? (
                            <img src={avatar} alt={name || 'User Avatar'} className="w-12 h-12 rounded-full object-cover" />
                        ) : (
                            <div className="w-12 h-12 rounded-full flex items-center justify-center text-2xl font-bold text-white bg-green-500">{initial}</div>
                        )}
                        <div className='flex flex-col justify-center'>
                            <div className="text-base font-semibold text-gray-900 dark:text-slate-100 truncate w-full">{name || 'Người dùng'}</div>
                            <div className="text-sm text-gray-500 dark:text-slate-400 truncate w-full">{email || 'Chưa có email'}</div>
                        </div>
                    </div>

                    <div className="w-full mt-3">
                        <div className="flex justify-between text-sm text-gray-700 dark:text-slate-300 mb-2">
                            <span>Đã sử dụng: {creditUse}/{credit}</span>
                            <span>{percent}%</span>
                        </div>
                        <div className="w-full h-2 bg-gray-200 rounded-full overflow-hidden">
                            <div
                                className="h-full bg-green-500 transition-all duration-300"
                                style={{ width: `${Math.min(percent, 100)}%` }}
                            />
                        </div>
                    </div>
                </div>

                <DropdownMenuSeparator />
                <DropdownMenuItem
                    onClick={handleLogout}
                    disabled={isLoggingOut}
                    className={`flex items-center gap-2 cursor-pointer ${isLoggingOut
                        ? 'text-gray-400 cursor-not-allowed'
                        : 'text-red-600 hover:text-red-700'
                        }`}
                >
                    {isLoggingOut ? (
                        <div className="mr-2 w-4 h-4 border-2 border-gray-300 border-t-gray-600 rounded-full animate-spin"></div>
                    ) : (
                        <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" fill="none" viewBox="0 0 24 24" stroke="currentColor" className="mr-2">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a2 2 0 01-2 2H7a2 2 0 01-2-2V7a2 2 0 012-2h4a2 2 0 012 2v1" />
                        </svg>
                    )}
                    {isLoggingOut ? 'Đang đăng xuất...' : 'Đăng xuất'}
                </DropdownMenuItem>
            </DropdownMenuContent>
        </DropdownMenu >
    );
};

export default UserProfileInfo;
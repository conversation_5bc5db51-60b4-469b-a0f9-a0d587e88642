"use client"

import type React from "react"
import { SessionProvider } from "next-auth/react"
import { ThemeProvider } from "@/components/theme-provider"
import { DomainProvider } from "@/components/providers/DomainProvider"

export function Providers({
  children
}: {
  children: React.ReactNode
}) {
  return (
    <SessionProvider>
      <DomainProvider>
        <ThemeProvider attribute="class" defaultTheme="system" enableSystem disableTransitionOnChange>
          {children}
        </ThemeProvider>
      </DomainProvider>
    </SessionProvider>
  )
}

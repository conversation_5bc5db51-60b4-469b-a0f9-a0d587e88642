"use client";

import { useDomainStore } from "@/stores/domainStore";

export const DomainDebug = () => {
  const { decodedDomainData, isDomainVerified } = useDomainStore();

  if (process.env.NODE_ENV !== "development") {
    return null;
  }

  return (
    <div className="fixed bottom-4 left-4 bg-blue-900 text-white p-4 rounded-lg text-xs max-w-sm z-50 max-h-96 overflow-y-auto">
      <h3 className="font-bold mb-2">Domain Debug</h3>
      <div className="space-y-1">
        <div>Domain Verified: {isDomainVerified ? "✅" : "❌"}</div>
        <div>Has Data: {decodedDomainData ? "✅" : "❌"}</div>
        {decodedDomainData && (
          <div className="mt-2 space-y-1">
            <div className="font-bold text-blue-300">Website Info:</div>
            <div>ID: {decodedDomainData.website?.website_id || "N/A"}</div>
            <div>Domain: {decodedDomainData.website?.domain || "N/A"}</div>
            <div className="font-bold text-blue-300 mt-2">Prompt Info:</div>
            <div>ID: {decodedDomainData.prompt?._id || "N/A"}</div>
            <div>Name: {decodedDomainData.prompt?.name || "N/A"}</div>
          </div>
        )}
      </div>
    </div>
  );
};

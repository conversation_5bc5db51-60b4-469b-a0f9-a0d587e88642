import { useSession, signOut } from "next-auth/react";
import { useEffect } from "react";
import { useUserStore } from "@/stores/userStore";
import { useAuthStore } from "@/stores/authStore";
import { getProfile } from "@/services/userProfileService";
import { getCookie, deleteCookie } from "cookies-next/client";

export const useAuth = () => {
    const { data: session, status } = useSession();
    const { profile, isLoading: profileLoading } = useUserStore();
    const { isAuthenticated, setAuth, logout: authLogout } = useAuthStore();

    // Ki<PERSON><PERSON> tra token trong cookie
    const token = getCookie("fchatai_token");

    // Xác định trạng thái đăng nhập
    const isLoggedIn = !!(session || token || isAuthenticated);
    const isLoading = status === "loading" || profileLoading;

    // Fetch profile khi có session hoặc token
    useEffect(() => {
        const fetchUserProfile = async () => {
            if ((session || token) && !profile) {
                try {
                    await getProfile();
                } catch (error) {
                    console.error("Failed to fetch profile:", error);
                }
            }
        };

        fetchUserProfile();
    }, [session, token, profile]);

    // Sync session với auth store
    useEffect(() => {
        if (session?.user && token) {
            setAuth(
                {
                    _id: "",
                    email: session.user.email || "",
                    name: session.user.name || "",
                    avatar: session.user.image || "",
                },
                token as string
            );
        }
    }, [session, token, setAuth]);

    // Logout function
    const logout = async () => {
        try {
            // Clear NextAuth session
            await signOut({ redirect: false });

            // Clear auth store
            authLogout();

            // Clear user profile
            useUserStore.getState().clearProfile();

            // Clear cookie
            deleteCookie("fchatai_token");

            console.log("✅ Logout successful");
        } catch (error) {
            console.error("❌ Logout error:", error);
        }
    };

    return {
        // States
        isLoggedIn,
        isLoading,
        session,
        profile,
        user: profile?.data?.user || session?.user,

        // Actions
        logout,

        // Helper getters
        hasProfile: !!profile,
        hasSession: !!session,
        hasToken: !!token,
    };
};
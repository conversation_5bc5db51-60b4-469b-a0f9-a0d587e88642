"use client";

import { ArrowLeft } from "lucide-react";
import Image from "next/image";
import { useRouter, useSearchParams } from "next/navigation";
import React, { useState, useEffect } from "react";
import { toast } from "sonner";
import { signIn } from "next-auth/react";
import GoogleOAuthError from "@/components/auth/GoogleOAuthError";

import { loginWithEmail, PayloadLoginEmail } from "@/services/authService";
import { useDomainStore } from "@/stores/domainStore";

export default function LoginPage() {
    const [showPassword, setShowPassword] = useState(false);
    const [isLoading, setIsLoading] = useState(false);
    const [showGoogleHelper, setShowGoogleHelper] = useState(false);
    const [oauthError, setOauthError] = useState<string | null>(null);
    const [formData, setFormData] = useState({
        email: "",
        password: "",
    });
    const router = useRouter();
    const searchParams = useSearchParams();
    const { isDomainVerified, decodedDomainData } = useDomainStore();

    // Kiểm tra lỗi OAuth từ URL params
    useEffect(() => {
        const error = searchParams.get("error");
        if (error) {
            setOauthError(error);
            setShowGoogleHelper(true);

            let errorMessage = "Lỗi xác thực.";
            switch (error) {
                case "GoogleSignInFailed":
                    errorMessage = "Đăng nhập Google thất bại, vui lòng thử lại.";
                    break;
                case "DomainNotVerified":
                    errorMessage = "Domain chưa được xác thực. Vui lòng kiểm tra lại.";
                    break;
                case "BackendLoginFailed":
                    errorMessage = "Lỗi từ server. Vui lòng thử lại sau.";
                    break;
                default:
                    errorMessage = "Lỗi xác thực không xác định.";
            }

            toast.error(errorMessage);
        }
    }, [searchParams]);

    const handleGoogleLogin = async () => {
        try {
            setIsLoading(true);
            setOauthError(null);
            setShowGoogleHelper(false);

            // Kiểm tra domain đã được verify chưa
            if (!isDomainVerified || !decodedDomainData?.website?.website_id) {
                toast.error("Domain chưa được xác thực. Vui lòng tải lại trang.");
                setIsLoading(false);
                return;
            }

            console.log("🔐 Starting Google OAuth flow...", {
                isDomainVerified,
                websiteId: decodedDomainData.website.website_id
            });

            // Gọi signIn của NextAuth để bắt đầu quy trình đăng nhập Google
            const result = await signIn("google", {
                callbackUrl: "/",
                redirect: true
            });

            // Nếu có lỗi trong quá trình signIn
            if (result?.error) {
                console.error("❌ Google OAuth error:", result.error);
                setShowGoogleHelper(true);
                toast.error("Đăng nhập Google thất bại, vui lòng thử lại.");
                setIsLoading(false);
            }
        } catch (error) {
            console.error("❌ Google login failed:", error);
            setShowGoogleHelper(true);
            toast.error("Đăng nhập Google thất bại, vui lòng thử lại.");
            setIsLoading(false);
        }
    };

    // Xử lý email/password login
    const handleEmailLogin = async (e: React.FormEvent) => {
        e.preventDefault();

        if (!formData.email || !formData.password) {
            toast.error("Vui lòng nhập đầy đủ email và mật khẩu");
            return;
        }

        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(formData.email)) {
            toast.error("Email không hợp lệ");
            return;
        }

        if (isLoading) {
            return;
        }
        setIsLoading(true);

        try {
            const payload: PayloadLoginEmail = {
                email: formData.email,
                password: formData.password,
            };

            const response = await loginWithEmail(payload);

            if (!response.error) {
                toast.success("Đăng nhập thành công");
                router.push("/");
            } else {
                toast.error(response.error || "Đăng nhập thất bại, vui lòng thử lại");
            }
        } catch (error: any) {
            const errorMessage = error.message || "Đã xảy ra lỗi, vui lòng thử lại";
            toast.error(errorMessage);
        } finally {
            setIsLoading(false);
        }
    };

    return (
        <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center p-4">
            <div className="w-full max-w-md">
                <div className="bg-white rounded-2xl shadow-xl p-8 space-y-6">
                    <div className="text-center">
                        <h1 className="text-2xl font-bold text-gray-900 mb-2">Đăng nhập</h1>
                        <p className="text-gray-600">Chọn phương thức đăng nhập</p>
                    </div>

                    {oauthError && (
                        <GoogleOAuthError
                            error={oauthError}
                            onRetry={() => {
                                setOauthError(null);
                                setShowGoogleHelper(false);
                            }}
                        />
                    )}

                    <div className="space-y-4">
                        <button
                            onClick={handleGoogleLogin}
                            type="button"
                            disabled={isLoading || !isDomainVerified}
                            className="w-full flex items-center justify-center gap-3 bg-white border border-gray-300 rounded-xl px-4 py-3 text-gray-700 font-medium hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                        >
                            <Image
                                src="https://developers.google.com/identity/images/g-logo.png"
                                alt="Google"
                                width={20}
                                height={20}
                            />
                            {isLoading ? "Đang đăng nhập..." :
                                !isDomainVerified ? "Đang xác thực domain..." :
                                    "Đăng nhập với Google"}
                        </button>

                        {showGoogleHelper && (
                            <div className="text-center p-3 bg-red-50 border border-red-200 rounded-lg">
                                <p className="text-sm text-red-600 mb-2">
                                    Đăng nhập Google thất bại. Vui lòng thử lại.
                                </p>
                                <button
                                    type="button"
                                    onClick={() => {
                                        setShowGoogleHelper(false);
                                    }}
                                    className="text-sm text-blue-600 hover:text-blue-700 font-medium"
                                >
                                    Thử lại
                                </button>
                            </div>
                        )}

                        <div className="relative">
                            <div className="absolute inset-0 flex items-center">
                                <div className="w-full border-t border-gray-300" />
                            </div>
                            <div className="relative flex justify-center text-sm">
                                <span className="px-2 bg-white text-gray-500">Hoặc</span>
                            </div>
                        </div>
                    </div>

                    <form className="space-y-4 w-full" onSubmit={handleEmailLogin}>
                        <div>
                            <label htmlFor="email" className="block text-sm font-medium mb-1">Email</label>
                            <input
                                id="email"
                                name="email"
                                type="email"
                                autoComplete="email"
                                required
                                value={formData.email}
                                onChange={(e) => setFormData((prev) => ({ ...prev, email: e.target.value }))}
                                className="flex h-10 w-full rounded-3xl border border-gray-300 bg-background px-3 py-2 text-base ring-offset-background placeholder:text-gray-400 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm"
                                placeholder="Nhập email"
                                disabled={isLoading}
                            />
                        </div>
                        <div>
                            <label htmlFor="password" className="block text-sm font-medium mb-1">Mật khẩu</label>
                            <div className="relative">
                                <input
                                    id="password"
                                    name="password"
                                    type={showPassword ? "text" : "password"}
                                    autoComplete="current-password"
                                    required
                                    value={formData.password}
                                    onChange={(e) => setFormData((prev) => ({ ...prev, password: e.target.value }))}
                                    className="flex h-10 w-full rounded-3xl border border-gray-300 bg-background px-3 py-2 text-base ring-offset-background placeholder:text-gray-400 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm pr-10"
                                    placeholder="Nhập mật khẩu"
                                    disabled={isLoading}
                                />
                                <button
                                    type="button"
                                    className="absolute inset-y-0 right-0 flex items-center pr-3"
                                    onClick={() => setShowPassword(!showPassword)}
                                >
                                    {showPassword ? (
                                        <svg className="h-4 w-4 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                                        </svg>
                                    ) : (
                                        <svg className="h-4 w-4 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21" />
                                        </svg>
                                    )}
                                </button>
                            </div>
                        </div>
                        <button
                            type="submit"
                            disabled={isLoading}
                            className="w-full bg-blue-600 hover:bg-blue-700 text-white font-medium py-3 px-4 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                        >
                            {isLoading ? "Đang đăng nhập..." : "Đăng nhập"}
                        </button>
                    </form>

                    <div className="text-center">
                        <button
                            type="button"
                            onClick={() => router.push("/")}
                            className="inline-flex items-center gap-2 text-sm text-gray-600 hover:text-gray-900 transition-colors"
                        >
                            <ArrowLeft className="h-4 w-4" />
                            Quay về trang chủ
                        </button>
                    </div>
                </div>
            </div>
        </div>
    );
}
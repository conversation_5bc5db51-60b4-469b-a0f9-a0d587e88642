import { getServerSession } from "next-auth/next";
import { authOptions } from "../[...nextauth]/route";
import { NextResponse } from "next/server";

export async function GET() {
  try {
    const session = await getServerSession(authOptions);

    // Trả về object rỗng thay vì null để tránh lỗi parsing
    if (!session) {
      return NextResponse.json({});
    }

    return NextResponse.json(session);
  } catch (error) {
    console.error("Session API error:", error);
    // Trả về object rỗng thay vì null
    return NextResponse.json({}, { status: 500 });
  }
}

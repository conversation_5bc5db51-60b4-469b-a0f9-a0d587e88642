// Message interfaces
export interface MessageData {
    _id: string;
    type: number;
    content: string;
    albums: any[];
    prompt_tokens: number;
    completion_tokens: number;
    total_tokens: number;
    conversation_id: string;
    created_at: string;
    updated_at: string;
    __v: number;
}

export interface MessageListResponse {
    error: boolean;
    status: number;
    msg: string;
    data: MessageData[];
}
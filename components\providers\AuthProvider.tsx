"use client";

import { SessionProvider } from "next-auth/react";
import { ReactNode } from "react";
import { ErrorBoundary } from "./ErrorBoundary";

interface AuthProviderProps {
  children: ReactNode;
}

export const AuthProvider = ({ children }: AuthProviderProps) => {
  return (
    <ErrorBoundary>
      <SessionProvider
        // Cấu hình để xử lý lỗi tốt hơn
        refetchInterval={5 * 60} // Refetch session mỗi 5 phút
        refetchOnWindowFocus={true}
        refetchWhenOffline={false}
      >
        {children}
      </SessionProvider>
    </ErrorBoundary>
  );
};

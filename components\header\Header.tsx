"use client";
import { ThemeToggle } from '@/components/ui/ThemeToggle'; import { useEffect, useState } from 'react';
import UserProfileInfo from '../profile/UserProfileInfo';
export default function Header() {


    return (
        <header className="sticky top-0 z-20 w-full border-b border-gray-200 bg-white dark:border-slate-700 dark:bg-gray-900 flex items-center justify-between p-4">
            <div className="flex items-center gap-3">
                <span className="flex size-9 rounded-full overflow-hidden">
                    <img src="/favicon.ico" alt="Logo Chat AI" className="object-cover" />
                </span>
                <h1 className="font-bold text-gray-800 dark:text-slate-100 text-lg">Chat AI</h1>
            </div>

            <div className="flex items-center justify-end gap-3 min-w-[200px]">
                <ThemeToggle />
                <UserProfileInfo />

            </div>
        </header>
    )
}  
